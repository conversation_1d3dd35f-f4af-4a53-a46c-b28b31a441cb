# flake8: noqa
from langchain_core.prompts.prompt import PromptTemplate

_DEFAULT_TEMPLATE = """Question: Who lived longer, <PERSON> or <PERSON>?
Are follow up questions needed here: Yes.
Follow up: How old was <PERSON> when he died?
Intermediate answer: <PERSON> was 74 years old when he died.
Follow up: How old was <PERSON> when he died?
Intermediate answer: <PERSON> was 41 years old when he died.
So the final answer is: <PERSON>

Question: When was the founder of craigslist born?
Are follow up questions needed here: Yes.
Follow up: Who was the founder of craigslist?
Intermediate answer: Craigslist was founded by <PERSON>.
Follow up: When was <PERSON> born?
Intermediate answer: <PERSON> was born on December 6, 1952.
So the final answer is: December 6, 1952

Question: Who was the maternal grandfather of <PERSON>?
Are follow up questions needed here: Yes.
Follow up: Who was the mother of <PERSON>?
Intermediate answer: The mother of <PERSON> was <PERSON>.
Follow up: Who was the father of <PERSON>?
Intermediate answer: The father of <PERSON> was <PERSON>.
So the final answer is: <PERSON>

Question: Are both the directors of Jaws and Casino Royale from the same country?
Are follow up questions needed here: Yes.
Follow up: Who is the director of Jaws?
Intermediate answer: The director of Jaws is <PERSON>.
Follow up: Where is <PERSON> from?
Intermediate answer: The United States.
Follow up: Who is the director of Casino Royale?
Intermediate answer: The director of Casino Royale is <PERSON>.
Follow up: Where is <PERSON> from?
Intermediate answer: New Zealand.
So the final answer is: No

Question: {input}
Are followup questions needed here:{agent_scratchpad}"""
PROMPT = PromptTemplate(
    input_variables=["input", "agent_scratchpad"], template=_DEFAULT_TEMPLATE
)
